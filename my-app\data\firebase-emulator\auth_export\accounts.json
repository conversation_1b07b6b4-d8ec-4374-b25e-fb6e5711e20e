{"kind": "identitytoolkit#DownloadAccountResponse", "users": [{"localId": "adminTestUser123", "createdAt": "*************", "lastLoginAt": "*************", "passwordHash": "fakeHash:salt=fakeSalt:password=admin123", "salt": "fakeSalt", "passwordUpdatedAt": **********656, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>"}], "validSince": "**********", "email": "<EMAIL>", "emailVerified": true, "disabled": false, "customClaims": {"admin": true, "roles": ["admin"], "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]}}, {"localId": "kUpCde9F1EC48ygdPct0YJktFQdC", "createdAt": "*************", "lastLoginAt": "*************", "passwordHash": "fakeHash:salt=fakeSaltOld:password=password", "salt": "fakeSaltOld", "passwordUpdatedAt": *************, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>"}], "validSince": "**********", "email": "<EMAIL>", "emailVerified": true, "disabled": false}]}